\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
library(exams)

# Generar datos aleatorios
ok <- FALSE
while(!ok) {
  # Datos básicos
  nombres <- c("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofía", "<PERSON>")
  familiares1 <- c("tío", "tía", "abuelo", "abuela")
  familiares2 <- c("tía", "tío", "abuela", "abuelo")
  
  # Seleccionar aleatoriamente
  nombre <- sample(nombres, 1)
  familiar1 <- "tio"
  familiar2 <- "tia"
  
  # Parámetros del problema
  ahorro_mensual <- sample(seq(125000, 200000, 25000), 1)
  meses <- sample(3:5, 1)
  
  # Porcentajes
  porcentaje_constante <- sample(8:12, 1)
  porcentajes_variables <- c(
    sample(1:5, 1),    # Mes 1: bajo
    sample(3:7, 1),    # Mes 2: medio
    sample(15:25, 1)   # Mes 3: alto
  )
  
  # Si hay más de 3 meses, agregar porcentajes adicionales
  if(meses > 3) {
    porcentajes_variables <- c(porcentajes_variables, sample(10:20, meses-3, replace=TRUE))
  }
  
  # Calcular ahorros acumulados
  ahorros_acumulados <- ahorro_mensual * (1:meses)
  
  # Calcular regalos para cada opción
  regalos_opcion1 <- ahorros_acumulados * porcentaje_constante / 100
  regalos_opcion2 <- ahorros_acumulados * porcentajes_variables / 100
  
  # Totales
  total_opcion1 <- sum(regalos_opcion1)
  total_opcion2 <- sum(regalos_opcion2)
  
  # Verificar que hay diferencia significativa
  diferencia <- abs(total_opcion1 - total_opcion2)
  if(diferencia > 10000) {
    ok <- TRUE
  }
}

# Determinar cuál opción es mejor
opcion_mejor <- ifelse(total_opcion1 > total_opcion2, 1, 2)
diferencia_final <- abs(total_opcion1 - total_opcion2)

# Crear opciones de respuesta
opciones <- character(4)
opciones[1] <- paste("El", familiar1, "ofrece más ayuda con un total de $", 
                     format(total_opcion1, big.mark=".", decimal.mark=","))
opciones[2] <- paste("La", familiar2, "ofrece más ayuda con un total de $", 
                     format(total_opcion2, big.mark=".", decimal.mark=","))
opciones[3] <- "Ambas opciones ofrecen la misma ayuda total"
opciones[4] <- "No se puede determinar cuál opción es mejor"

# Determinar respuesta correcta
if(opcion_mejor == 1) {
  solucion <- c(TRUE, FALSE, FALSE, FALSE)
} else {
  solucion <- c(FALSE, TRUE, FALSE, FALSE)
}

# Crear explicaciones
explicaciones <- character(4)
explicaciones[1] <- ifelse(solucion[1], "Correcto. Los cálculos muestran que esta opción da más dinero.", 
                          "Incorrecto. Revisa los cálculos totales.")
explicaciones[2] <- ifelse(solucion[2], "Correcto. Los cálculos muestran que esta opción da más dinero.", 
                          "Incorrecto. Revisa los cálculos totales.")
explicaciones[3] <- "Incorrecto. Las opciones tienen totales diferentes."
explicaciones[4] <- "Incorrecto. Se puede calcular y comparar ambas opciones."
@

\begin{question}
\Sexpr{nombre} quiere ahorrar $\Sexpr{format(ahorro_mensual, big.mark=".", decimal.mark=",")} cada mes durante \Sexpr{meses} meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.

\textbf{Opción 1 (\Sexpr{familiar1}):} Al finalizar cada mes, su \Sexpr{familiar1} le regala un porcentaje del dinero que tenga acumulado en su inversión.

\begin{center}
\begin{tabular}{|c|c|c|}
\hline
\textbf{Ahorro Acumulado} & \textbf{Mes} & \textbf{Porcentaje regalado por \Sexpr{familiar1}} \\
\hline
<<echo=FALSE, results=tex>>=
for(i in 1:meses) {
  cat("$", format(ahorros_acumulados[i], big.mark=".", decimal.mark=","), " & ", i, " & ", porcentaje_constante, "\\% \\\\\n")
  cat("\\hline\n")
}
@
\end{tabular}
\end{center}

\textbf{Opción 2 (\Sexpr{familiar2}):} Al finalizar cada mes, su \Sexpr{familiar2} le regala un porcentaje del dinero que tenga acumulado en su inversión.

\begin{center}
\begin{tabular}{|c|c|c|}
\hline
\textbf{Ahorro Acumulado} & \textbf{Mes} & \textbf{Porcentaje regalado por \Sexpr{familiar2}} \\
\hline
<<echo=FALSE, results=tex>>=
for(i in 1:meses) {
  cat("$", format(ahorros_acumulados[i], big.mark=".", decimal.mark=","), " & ", i, " & ", porcentajes_variables[i], "\\% \\\\\n")
  cat("\\hline\n")
}
@
\end{tabular}
\end{center}

\Sexpr{nombre} debe elegir la opción que le regalen la mayor cantidad de dinero. ¿Cuál opción debe elegir?

<<echo=FALSE, results=tex>>=
answerlist(opciones)
@
\end{question}

\begin{solution}
Para resolver este problema, debemos calcular la ayuda total que recibiría \Sexpr{nombre} con cada opción.

\textbf{Opción 1 (\Sexpr{familiar1}):}

<<echo=FALSE, results=tex>>=
for(i in 1:meses) {
  cat("Mes ", i, ": $", format(ahorros_acumulados[i], big.mark=".", decimal.mark=","), 
      " $\\times$ ", porcentaje_constante, "\\% = $", 
      format(regalos_opcion1[i], big.mark=".", decimal.mark=","), "\\\\\n")
}
cat("Total ", familiar1, ": $", format(total_opcion1, big.mark=".", decimal.mark=","), "\\\\\n")
@

\textbf{Opción 2 (\Sexpr{familiar2}):}

<<echo=FALSE, results=tex>>=
for(i in 1:meses) {
  cat("Mes ", i, ": $", format(ahorros_acumulados[i], big.mark=".", decimal.mark=","), 
      " $\\times$ ", porcentajes_variables[i], "\\% = $", 
      format(regalos_opcion2[i], big.mark=".", decimal.mark=","), "\\\\\n")
}
cat("Total ", familiar2, ": $", format(total_opcion2, big.mark=".", decimal.mark=","), "\\\\\n")
@

Por lo tanto, la respuesta correcta es que \Sexpr{if(opcion_mejor == 1) paste("el", familiar1) else paste("la", familiar2)} ofrece más ayuda.

<<echo=FALSE, results=tex>>=
answerlist(explicaciones)
@
\end{solution}

%% META-INFORMATION
%% \extype{schoice}
%% \exsolution{\Sexpr{mchoice2string(solucion)}}
%% \exname{Ahorro con porcentajes}

\end{document}
